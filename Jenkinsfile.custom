pipeline {
    agent any
    
    stages {
        stage('Build Docker Image') {
            steps {
                dir('docker') {
                    script {
                        sh 'docker-compose build'
                    }
                }
            }
        }
        
        stage('Run Tests') {
            steps {
                dir('docker') {
                    script {
                        // Run tests (always continue even if some tests fail)
                        sh 'bash quick-test.sh || true'
                    }
                }
            }
        }
        
        stage('Check Results') {
            steps {
                dir('docker') {
                    script {
                        // Check results and set build status
                        def result = sh(script: 'bash check-results.sh', returnStatus: true)
                        
                        if (result == 1) {
                            currentBuild.result = 'FAILURE'
                            error('Tests failed: Fail rate > 50%')
                        } else if (result == 2) {
                            currentBuild.result = 'UNSTABLE'
                            echo 'Tests unstable: Fail rate > 30%'
                        } else {
                            echo 'Tests passed: Fail rate ≤ 30%'
                        }
                    }
                }
            }
        }
    }
    
    post {
        always {
            // Archive results
            archiveArtifacts artifacts: 'docker/results/**/*', allowEmptyArchive: true
            
            // Publish HTML report
            publishHTML([
                allowMissing: false,
                alwaysLinkToLastBuild: true,
                keepAll: true,
                reportDir: 'docker/results',
                reportFiles: 'report.html',
                reportName: 'Test Report'
            ])
            
            // Cleanup
            dir('docker') {
                sh 'docker-compose down || true'
            }
        }
    }
}
