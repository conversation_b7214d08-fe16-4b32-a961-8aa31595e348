*** Settings ***
Resource   ../locator/course_locator.robot
Resource    ../../resources/browser_setup.robot
Library    SeleniumLibrary
Library    String
Library    Collections
Library    RPA.Desktop
*** Keywords ***
Select Value Programe In Course Form
    [Arguments]    ${PROGRAM_VL}
    Select From List By Label    id:id_program    ${PROGRAM_VL}
Input Course Code
    [Arguments]    ${COURSE_CODE}
    Input Text    ${COURSECODE_INPUT}    ${COURSE_CODE}

Input Course Name
    [Arguments]    ${COURSE_NAME}
    Input Text    ${COURSENAME_INPUT}    ${COURSE_NAME}

Input Credits
    [Arguments]    ${CREDITS}
    Input Text    ${CREDITS_INPUT}    ${CREDITS}
Input Description In Course Form
    [Arguments]    ${DESCRIPTION}
    Input Text    ${DESCRIPTION_INPUT}    ${DESCRIPTION}
Select value course type
    [Arguments]    ${TYPE_VL}
    Click Element    ${TYPE_SELECT}
    Select From List By Label    id:id_type    ${TYPE_VL}
Select value status in course form
    [Arguments]    ${STATUS_VL}
    Select From List By Label    ${STATUS_SELECT}    ${STATUS_VL}
Select value prerequisite in course form
    [Arguments]    ${PREREQUISITE_VL}
    Select From List By Label    ${PREREQUISITE}    ${PREREQUISITE_VL}
Click Checkbox Coresubject
    Click Element    ${CORESUBJECT_CKB}

Scroll to Create Course Button
    Scroll Element Into View    ${CREATECOURSE_BTN}

Click Create Course Button
    Click Element    ${CREATECOURSE_BTN}

Click Trang chu in left menu
    Wait Until Element Is Visible    ${TRANGCHU_MENUBAR}    timeout=10s
    Click Element    ${TRANGCHU_MENUBAR}

Click Quan ly hoc phan button
    Scroll Element Into View    ${COURSEMANAGEMENT_BTN}
    Click Element    ${COURSEMANAGEMENT_BTN}
    Wait Until Page Contains    All Course
    
Click Add course button
    Click Element    ${ADDCOURSE_BTN}

Click Programs button
    Click Element    ${PROGRAM_BTN}
Click Courses button in table
    Click Element    ${COURSES_BTN}
Click CLOs button
    Click Element    ${CLOs_BTN}
Click Add CLOs button in CLOs frame
    Click Element    ${ADDCLO_BTN}
Click Create CLOs button
    Click Element    ${CREATECLO_BTN}

Select Value Course In CLOs Form
    [Arguments]    ${COURSE_VL}
    Wait Until Element Is Visible    ${COURSE_SELECT}    timeout=10s
    Select From List By Label    ${COURSE_SELECT}    ${COURSE_VL}

# Select Value Programe In Course Form    
#     [Arguments]    ${PROGRAME_VL}
#     Wait Until Element Is Visible    ${PROGRAME_SELECT}    timeout=10s
#     Select From List By Label    ${PROGRAME_SELECT}    ${PROGRAME_VL}
Input Text CLO Code
    [Arguments]    ${CLO_CODE_VL}
    Input Text    ${CLO_CODE}    ${CLO_CODE_VL}
Input Text Description In CLOs Form
    [Arguments]    ${DESCRIPTION_VL}
    Input Text    ${DESCRIPTION_CLOs}    ${DESCRIPTION_VL}
Scroll to Create CLO Button
    Wait Until Element Is Visible    ${CREATECLO_BTN}
    Scroll Element Into View    ${CREATECLO_BTN}
Click Create CLO Button
    Scroll to Create CLO Button
    Click Button    ${CREATECLO_BTN}

Select From List By Label Category In CLOs Form
    [Arguments]    ${CATEGORY_VL}
    Select From List By Label    ${CATEGORY_SELECT}    ${CATEGORY_VL}
Select From List By Label Level In CLOs Form
    [Arguments]    ${LEVEL_VL}
    Select From List By Label    ${LEVEL_SELECT}    ${LEVEL_VL}
Input Text Weight In CLOs Form
    [Arguments]    ${WEIGHT_VL}
    Input Text    ${WEIGHT_INPUT}    ${WEIGHT_VL}
Select From List By Label Assessment Method In CLOs Form
    [Arguments]    ${ASSESSMENT_METHOD_VL}
    Select From List By Label    ${ASSESSMENT_METHOD_SELECT}    ${ASSESSMENT_METHOD_VL}

Get value required field error
    Wait Until Page Contains    This field is required.   timeout=10s
    ${ele}    Get WebElements    ${REQUIRED_FIELD_ERROR}
    ${count}    Get Length    ${ele}
    Log    Số lượng trường bắt buộc phải điền: ${count}

Display table course    
    Wait Until Element Is Visible    ${COURSE_TABLE_DATA}    timeout=10s

Click update button in course page_object    
    Click Button    ${UPDATECOURSE_BTN} 
Scroll to Update Course Button
    Scroll Element Into View    ${UPDATECOURSE_BTN}
    Wait Until Element Is Visible    ${UPDATECOURSE_BTN}    timeout=10s
##    
# Select value course
#     Select Checkbox    ${CORESUBJECT_CKB}



# Enter Value Course Name
#     [Arguments]    ${COURSE_NAME}
#     Input Text    ${COURSENAME_INPUT}    ${COURSE_NAME}
# Enter Value Course Code
#     [Arguments]    ${COURSE_CODE}
#     Input Text    ${COURSECODE_INPUT}    ${COURSE_CODE}    
Go to create course
    Click Trang chu in left menu
    Click Quan ly hoc phan button
    Click Programs button
    Click Courses button in table
    Click Add course button

Enter Course Detail Form
    [Arguments]    ${PROGRAME_VL}    ${COURSE_CODE_VL}    ${COURSE_NAME_VL}    ${CREDITS_VL}    
    ...    ${DESCRIPTION_VL}    ${TYPE_VL}    ${STATUS_VL}    ${PREREQUISITE_VL}
    #Select From List By Label    ${PROGRAME_SELECT}
    Select Value Programe In Course Form    ${PROGRAME_VL}
    Input Course Code    ${COURSE_CODE_VL}
    Input Course Name    ${COURSE_NAME_VL}
    Input Credits    ${CREDITS_VL}
    Input Description In Course Form    ${DESCRIPTION_VL}    
    Scroll to Create Course Button
    Select value course type    ${TYPE_VL}            
    # Select From List By Label    id:id_type   
    Click Checkbox Coresubject
    Select value status in course form    ${STATUS_VL}
    Select value prerequisite in course form    ${PREREQUISITE_VL}
    
Enter Course Detail Form With Maximum Credits
    [Arguments]    ${PROGRAM_VL}    ${COURSE_CODE_VL}    ${COURSE_NAME_VL}        
    ...    ${DESCRIPTION_VL}    ${TYPE_VL}    ${STATUS_VL}    ${PREREQUISITE_VL}
    #Select From List By Label    ${PROGRAME_SELECT}
    Select Value Programe In Course Form    ${PROGRAM_VL}
    Input Course Code    ${COURSE_CODE_VL}
    Input Course Name    ${COURSE_NAME_VL}
    # Input Text    ${CREDITS_INPUT}    ${CREDITS_VL}   
    # ${prev}=    Get Value    ${CREDITS_INPUT}
    # ${prev}=    Convert To Integer    ${prev}

    FOR    ${value}    IN    @{TEST_VALUES}
        Clear Element Text    ${CREDITS_INPUT}
        Input Text    ${CREDITS_INPUT}    ${value}
        #Click Element    ${CREATECOURSE_BTN}
        Sleep    0.5s
        ${is_success}=    Run Keyword And Return Status    Element Should Be Visible    Giá trị hợp lệ
        ${is_error}=      Run Keyword And Return Status    Element Should Be Visible    ${ERROR_MSG}
        Run Keyword If    ${is_success}    Log    ${value} là giá trị hợp lệ
        Run Keyword If    ${is_error}      Log    ${value} là giá trị không hợp lệ
    END
    
    Input Description In Course Form    ${DESCRIPTION_VL}
    Scroll to Create Course Button
    Select value course type    ${TYPE_VL}        
    # Select From List By Label    id:id_type   
    Click Checkbox Coresubject
    Select value status in course form    ${STATUS_VL}
    Select value prerequisite in course form    ${PREREQUISITE_VL}
    Click Create Course Button

Empty Required Fields With Course Form
    Click Checkbox Coresubject
    Scroll to Create Course Button
    # Select value prerequisite in course form    ${PREREQUISITE_VL}
    Click Create Course Button
    Wait Until Page Contains    This field is required.   timeout=10s
    ${ele}    Get WebElements    //p[contains(.,'This field is required.')]
    ${count}    Get Length    ${ele}
    Log    Số lượng trường bắt buộc phải điền: ${count}

Edit Course Detail Form
    [Arguments]    ${PROGRAME_VL}    ${COURSE_CODE_VL}    ${COURSE_NAME_VL}    ${CREDITS_VL}    
    ...    ${DESCRIPTION_VL}    ${TYPE_VL}    ${STATUS_VL}    ${PREREQUISITE_VL}
    #Select From List By Label    ${PROGRAME_SELECT}
    Select Value Programe In Course Form    ${PROGRAME_VL}
    Input Course Code    ${COURSE_CODE_VL}
    Input Course Name    ${COURSE_NAME_VL}
    Input Credits    ${CREDITS_VL}
    Input Description In Course Form    ${DESCRIPTION_VL}    
    Scroll to Update Course Button
    Select value course type    ${TYPE_VL}            
    # Select From List By Label    id:id_type   
    Click Checkbox Coresubject
    Select value status in course form    ${STATUS_VL}
    Select value prerequisite in course form    ${PREREQUISITE_VL}
Go To Create CLO
    Click Trang chu in left menu
    Click Quan ly hoc phan button
    Click Programs button
    Click Courses button in table
    Click CLOs button
    Click Add CLOs button in CLOs frame

Enter Course Learning Outcome Form
    [Arguments]    ${COURSE_VL}    ${CLO_CODE_VL}    ${DESCRIPTION_VL}    
    ...    ${CATEGORY_VL}    ${LEVEL_VL}    ${WEIGHT_VL}    ${ASSESSMENT_METHOD_VL}
    Select Value Course In CLOs Form    ${COURSE_VL}
    Input Text CLO Code    ${CLO_CODE_VL}
    Input Text Description In CLOs Form    ${DESCRIPTION_VL}
    Scroll to Create CLO Button
    Select From List By Label Category In CLOs Form    ${CATEGORY_VL}
    Select From List By Label Level In CLOs Form    ${LEVEL_VL}
    Input Text Weight In CLOs Form    ${WEIGHT_VL}
    Select From List By Label Assessment Method In CLOs Form    ${ASSESSMENT_METHOD_VL}
    
Empty Required Fields With CLOs
    Click Create CLO Button
    Get value required field error

Go To CLO
    Click Element    ${HOCPHAN_MENUBAR}
    Click Element    ${CLOs_BTN}

View Course Name From Table
    Click Trang chu in left menu
    Click Quan ly hoc phan button
    Click Programs button
    Click Courses button in table
    # Click Element    ${PROGRAM_BTN}
    # Click Element    ${COURSES_BTN}
    Display table course

# Save Course Table Data To Excel
#     ${cols}=    Get Element Count    xpath=//table/thead/tr/th
#     @{headers}=    Create List
#     FOR    ${j}    IN RANGE    1    ${cols}+1
#         ${header}=    Get Text    xpath=//table/thead/tr/th[${j}]
#         Append To List    ${headers}    ${header}
#     END

#     ${rows}=    Get Element Count    xpath=//table/tbody/tr

#     @{all_rows}=    Create List
#     Append To List    ${all_rows}    ${headers}

#     FOR    ${i}    IN RANGE    1    ${rows}+1
#         @{row}=    Create List
#         FOR    ${j}    IN RANGE    1    ${cols}+1
#             ${cell}=    Get Text    xpath=//table/tbody/tr[${i}]/td[${j}]
#             Append To List    ${row}    ${cell}
#         END
#         Append To List    ${all_rows}    ${row}
#     END

    
#     ${file_exist}=    Run Keyword And Return Status    File Should Exist    ${EXCEL_FILE}
#     IF    ${file_exist}
#         Open Workbook    ${EXCEL_FILE}
#     ELSE
#         Create Workbook    ${EXCEL_FILE}
#     END
    
#     Create Worksheet   ${SHEET_NAME_COURSE}    header=${False}
#     Append Rows To Worksheet    ${all_rows}    header=${False}
    
#     Save Workbook    ${EXCEL_FILE}
#     Close Workbook

# Save CLO Table Data To Excel
#     ${cols}=    Get Element Count    xpath=//table/thead/tr/th
#     @{headers}=    Create List
#     FOR    ${j}    IN RANGE    1    ${cols}+1
#         ${header}=    Get Text    xpath=//table/thead/tr/th[${j}]
#         Append To List    ${headers}    ${header}
#     END

#     ${rows}=    Get Element Count    xpath=//table/tbody/tr

#     @{all_rows}=    Create List
#     Append To List    ${all_rows}    ${headers}

#     FOR    ${i}    IN RANGE    1    ${rows}+1
#         @{row}=    Create List
#         FOR    ${j}    IN RANGE    1    ${cols}+1
#             ${cell}=    Get Text    xpath=//table/tbody/tr[${i}]/td[${j}]
#             Append To List    ${row}    ${cell}
#         END
#         Append To List    ${all_rows}    ${row}
#     END

#     ${file_exist}=    Run Keyword And Return Status    File Should Exist    ${EXCEL_FILE}
#     IF    ${file_exist}
#         Open Workbook    ${EXCEL_FILE}
#     ELSE
#         Create Workbook    ${EXCEL_FILE}
#     END
    
#     Create Worksheet   ${SHEET_NAME_CLO}    header=${False}
#     Append Rows To Worksheet    ${all_rows}    header=${False}
    
#     Save Workbook    ${EXCEL_FILE}
#     Close Workbook


Edit Course
    Click Element    ${EDITCOURSE_BTN}
    Wait Until Element Is Visible    ${COURSECODE_INPUT}    timeout=10s
    Scroll Element Into View    ${UPDATECOURSE_BTN}
    Click update button in course page_object    


Compare value CLO
    ${value}    Get Text    ${VALUE_CLO}

    ${ele}    Get WebElements    ${LIST_CLO_IN_COURSE}
    ${count}    Get Length    ${ele}

    # Should Be Equal As Strings    ${value}    ${count} 
    Should Be Equal As Integers    ${value}    ${count}    Giá trị CLO trong khoá học không khớp với số lượng CLO đã tạo: ${value} != ${count} 
    # Should Be True    ${value} == ${count}    Giá trị CLO trong khoá học: ${value} = ${count} khớp với số lượng CLO đã tạo
