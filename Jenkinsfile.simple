pipeline {
    agent any
    
    stages {
        stage('Test') {
            steps {
                dir('docker') {
                    sh 'docker-compose build'
                    sh 'bash quick-test.sh'
                }
            }
        }
    }
    
    post {
        always {
            archiveArtifacts artifacts: 'docker/results/**/*', allowEmptyArchive: true
            publishHTML([
                allowMissing: false,
                alwaysLinkToLastBuild: true,
                keepAll: true,
                reportDir: 'docker/results',
                reportFiles: 'report.html',
                reportName: 'Test Report'
            ])
            dir('docker') {
                sh 'docker-compose down || true'
            }
        }
    }
}
