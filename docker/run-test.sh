#!/bin/bash
# Fast Robot Framework test runner

set -e
cd "$(dirname "$0")"

# Parse arguments
TEST_FILE=""
BUILD=""
while [[ $# -gt 0 ]]; do
    case $1 in
        --test) TEST_FILE="$2"; shift 2 ;;
        --build) BUILD="--build"; shift ;;
        *) echo "Usage: $0 [--test FILE] [--build]"; exit 1 ;;
    esac
done

# Clean up and run
docker-compose down --remove-orphans 2>/dev/null || true

if [[ -n "$BUILD" ]]; then
    docker-compose build
fi

# Run tests
if [[ -n "$TEST_FILE" ]]; then
    docker-compose run --rm robot-tests python run_tests.py --test "$TEST_FILE"
else
    docker-compose run --rm robot-tests
fi

# Cleanup
docker-compose down 2>/dev/null || true