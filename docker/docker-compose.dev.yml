version: '3.8'

services:
  robot-tests-dev:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    image: robot-framework-tests:latest
    container_name: robot-framework-tests-dev
    volumes:
      # Mount results directory to persist test results
      - ./results:/app/results
      # Mount source code for development (live reload)
      - ../test:/app/test
      - ../test_runner:/app/test_runner
      - ../resources:/app/resources
      - ../pages:/app/pages
      - ../libs:/app/libs
      - ../run_tests.py:/app/run_tests.py
    environment:
      - DISPLAY=:99
      - PYTHONUNBUFFERED=1
      - ROBOT_REPORTS_DIR=/app/results
      - ROBOT_TESTS_DIR=/app/test
      - DOCKER_ENV=true
    # Keep container running for development
    restart: "no"
    tty: true
    stdin_open: true
    # Override command for development
    command: ["tail", "-f", "/dev/null"]

  # Optional: Results viewer for development
  results-viewer:
    image: nginx:alpine
    container_name: robot-results-viewer-dev
    ports:
      - "8080:80"
    volumes:
      - ./results:/usr/share/nginx/html:ro
    depends_on:
      - robot-tests-dev
    profiles:
      - viewer
