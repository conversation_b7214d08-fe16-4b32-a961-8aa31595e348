#!/usr/bin/env python3
"""
Simple script to test Chrome and ChromeDriver compatibility in Docker
"""
import sys
import os
sys.path.insert(0, '/app')

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from libs.browser_options import get_chrome_options

def test_chrome_setup():
    """Test Chrome and ChromeDriver setup"""
    print("Testing Chrome and ChromeDriver setup...")
    print("=" * 50)
    
    try:
        # Test 1: Check Chrome version
        print("1. Checking Chrome version...")
        import subprocess
        chrome_version = subprocess.check_output(['google-chrome', '--version']).decode().strip()
        print(f"   Chrome version: {chrome_version}")
        
        # Test 2: Check ChromeDriver version
        print("2. Checking ChromeDriver version...")
        chromedriver_version = subprocess.check_output(['chromedriver', '--version']).decode().strip()
        print(f"   ChromeDriver version: {chromedriver_version}")
        
        # Test 3: Test Chrome options
        print("3. Testing Chrome options...")
        options = get_chrome_options()
        print(f"   Chrome options created successfully")
        print(f"   Number of arguments: {len(options.arguments)}")
        
        # Test 4: Test WebDriver initialization
        print("4. Testing WebDriver initialization...")
        driver = webdriver.Chrome(options=options)
        print("   WebDriver initialized successfully")
        
        # Test 5: Test basic navigation
        print("5. Testing basic navigation...")
        driver.get("https://www.google.com")
        title = driver.title
        print(f"   Page title: {title}")
        
        # Test 6: Test JavaScript execution
        print("6. Testing JavaScript execution...")
        result = driver.execute_script("return 'JavaScript works!'")
        print(f"   JavaScript result: {result}")
        
        # Cleanup
        driver.quit()
        print("\n✅ All tests passed! Chrome setup is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_chrome_setup()
    sys.exit(0 if success else 1)
