# Docker Chrome WebDriver Fix

## Vấn đề gốc

<PERSON>hi chạy Robot Framework tests trong Docker, gặp lỗi:
```
WebDriverException: Message: unknown error: JavaScript code failed
from unknown command: 'Runtime.evaluate' wasn't found
(Session info: chrome=138.0.7204.183)
```

## Nguyên nhân

1. **Chrome và ChromeDriver không tương thích**: Chrome version 138 không có ChromeDriver tương ứng trong API cũ
2. **Chrome options chưa đủ cho Docker**: Thiếu các options quan trọng cho môi trường container
3. **API ChromeDriver đã thay đổi**: Google đã thay đổi cách phân phối ChromeDriver

## Giải pháp đã áp dụng

### 1. Cập nhật Dockerfile

**Trước:**
```dockerfile
CHROMEDRIVER_VERSION=$(curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_$CHROME_VERSION" || echo "131.0.6778.85")
wget -O /tmp/chromedriver.zip "https://chromedriver.storage.googleapis.com/${CHROMEDRIVER_VERSION}/chromedriver_linux64.zip"
```

**Sau:**
```dockerfile
CHROMEDRIVER_URL="https://googlechromelabs.github.io/chrome-for-testing/last-known-good-versions-with-downloads.json"
CHROMEDRIVER_VERSION=$(curl -s $CHROMEDRIVER_URL | grep -o '"version":"[^"]*' | head -1 | cut -d'"' -f4)
wget -O /tmp/chromedriver.zip "https://storage.googleapis.com/chrome-for-testing-public/${CHROMEDRIVER_VERSION}/linux64/chromedriver-linux64.zip"
```

### 2. Cải thiện Chrome Options

**Thêm vào `libs/browser_options.py`:**

```python
# Detect Docker environment
is_docker = os.path.exists('/.dockerenv') or os.environ.get('DOCKER_ENV') == 'true'

# Docker-specific options
if is_docker:
    options.add_argument("--headless=new")  # Headless mode for Docker
    options.add_argument("--single-process")
    options.add_argument("--no-zygote")
    options.add_argument("--disable-background-networking")
    # ... more Docker-specific options
```

### 3. Cải thiện Xvfb startup

**Trước:**
```bash
Xvfb :99 -screen 0 1920x1080x24 &
sleep 2
```

**Sau:**
```bash
Xvfb :99 -screen 0 1920x1080x24 -ac +extension GLX +render -noreset &
XVFB_PID=$!
# Verify Xvfb is running
if ! ps -p $XVFB_PID > /dev/null; then
    echo "Failed to start Xvfb"
    exit 1
fi
```

### 4. Thêm biến môi trường Docker

```dockerfile
ENV DOCKER_ENV=true
```

```yaml
environment:
  - DOCKER_ENV=true
```

## Kết quả

- ✅ Chrome và ChromeDriver tương thích (version 138.0.7204.183)
- ✅ JavaScript execution hoạt động bình thường
- ✅ Tất cả 5 tests pass (100% success rate)
- ✅ Không còn lỗi WebDriverException

## Cách sử dụng

### Build và chạy tests:
```bash
cd docker
docker-compose build --no-cache
docker-compose run --rm robot-tests
```

### Test Chrome setup:
```bash
docker-compose run --rm robot-tests python test-chrome.py
```

### Chạy script test tự động:
```bash
./test-docker-setup.sh
```

## Các file đã thay đổi

1. `docker/Dockerfile` - Cập nhật cách cài ChromeDriver và cải thiện Xvfb
2. `libs/browser_options.py` - Thêm Docker-specific Chrome options
3. `docker/docker-compose.yml` - Thêm biến môi trường DOCKER_ENV
4. `docker/docker-compose.dev.yml` - Thêm biến môi trường DOCKER_ENV
5. `docker/test-chrome.py` - Script test Chrome setup (mới)
6. `docker/test-docker-setup.sh` - Script test tự động (mới)

## Lưu ý

- Chrome sẽ tự động chạy ở headless mode trong Docker
- Xvfb được sử dụng để cung cấp display ảo
- ChromeDriver version được tự động sync với Chrome version
- Tất cả tests hiện tại đều pass mà không cần thay đổi test code
