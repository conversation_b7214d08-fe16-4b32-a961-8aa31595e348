version: '3.8'

services:
  robot-tests:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    image: robot-framework-tests:latest
    container_name: robot-framework-tests
    volumes:
      # Mount results directory to persist test results
      - ./results:/app/results
    environment:
      - DISPLAY=:99
      - PYTHONUNBUFFERED=1
      - ROBOT_REPORTS_DIR=/app/results
      - ROBOT_TESTS_DIR=/app/test
    # Remove the container after execution
    restart: "no"
    # Allocate pseudo-TTY for better output
    tty: true
