#!/bin/bash

# Test script for Docker Chrome setup
# Usage: ./test-docker-setup.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Change to docker directory
cd "$(dirname "$0")"

print_header "Testing Docker Chrome Setup"
echo "=" * 50

# Step 1: Clean up existing containers and images
print_header "Step 1: Cleaning up existing containers and images"
docker-compose down --remove-orphans 2>/dev/null || true
docker rmi robot-framework-tests:latest 2>/dev/null || true

# Step 2: Build the Docker image
print_header "Step 2: Building Docker image"
docker-compose build --no-cache

# Step 3: Test Chrome setup
print_header "Step 3: Testing Chrome and ChromeDriver setup"
docker-compose run --rm robot-tests python test-chrome.py

# Step 4: Run a simple Robot Framework test
print_header "Step 4: Running a simple Robot Framework test"
if [ -f "../test/question_tests.robot" ]; then
    print_status "Running question tests..."
    docker-compose run --rm robot-tests robot --outputdir /app/results --loglevel DEBUG /app/test/question_tests.robot
else
    print_warning "No test file found, skipping Robot Framework test"
fi

# Step 5: Check results
print_header "Step 5: Checking results"
if [ -f "./results/output.xml" ]; then
    print_status "Test results found!"
    print_status "Check ./results/report.html for detailed results"
    
    # Show basic test statistics
    if command -v python3 &> /dev/null; then
        python3 -c "
import xml.etree.ElementTree as ET
try:
    tree = ET.parse('./results/output.xml')
    root = tree.getroot()
    stats = root.find('.//total/stat')
    if stats is not None:
        print(f'Tests: {stats.get(\"pass\", \"0\")} passed, {stats.get(\"fail\", \"0\")} failed')
    else:
        print('Could not parse test statistics')
except Exception as e:
    print(f'Error parsing results: {e}')
"
    fi
else
    print_warning "No test results found"
fi

# Cleanup
print_header "Cleaning up"
docker-compose down 2>/dev/null || true

print_status "Docker setup test completed!"
