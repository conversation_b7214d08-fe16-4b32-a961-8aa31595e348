# Fast Robot Framework Docker

## Quick Start

```bash
cd docker

# First time - build image
./run-tests.sh --build

# Run all tests (fast)
./quick-test.sh

# Run specific test
./quick-test.sh python run_tests.py --test filename.robot
```

## Files

- `Dockerfile` - Minimal Docker image (~500MB)
- `docker-compose.yml` - Simple compose config
- `run-tests.sh` - Full runner with build option
- `quick-test.sh` - Ultra-fast runner (no cleanup)

## Optimizations Applied

- ✅ Removed dev files and documentation
- ✅ Minimal Chrome options for speed
- ✅ Simplified Xvfb startup
- ✅ Reduced Docker layers
- ✅ Fast Chrome options for Docker
- ✅ No unnecessary cleanup in quick mode

## Results

Tests run in ~30-60 seconds (after first build).
Results saved to `./results/` directory.
