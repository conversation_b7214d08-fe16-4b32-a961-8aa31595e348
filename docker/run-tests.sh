#!/bin/bash

# Quick test runner script for Docker
# Usage: ./run-tests.sh [test_file] [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
TEST_FILE=""
DOCKER_COMPOSE_FILE="docker-compose.yml"
BUILD_CACHE="--build"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dev)
            DOCKER_COMPOSE_FILE="docker-compose.dev.yml"
            shift
            ;;
        --no-build)
            BUILD_CACHE=""
            shift
            ;;
        --test)
            TEST_FILE="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --dev           Use development docker-compose file"
            echo "  --no-build      Skip building the image"
            echo "  --test FILE     Run specific test file"
            echo "  -h, --help      Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Change to docker directory
cd "$(dirname "$0")"

print_status "Starting Robot Framework tests..."
print_status "Using compose file: $DOCKER_COMPOSE_FILE"

# Clean up any existing containers
print_status "Cleaning up existing containers..."
docker-compose -f "$DOCKER_COMPOSE_FILE" down --remove-orphans 2>/dev/null || true

# Build and run tests
if [[ -n "$BUILD_CACHE" ]]; then
    print_status "Building Docker image..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" build --no-cache
fi

print_status "Running tests..."

# Run the container
if [[ -n "$TEST_FILE" ]]; then
    print_status "Running specific test: $TEST_FILE"
    docker-compose -f "$DOCKER_COMPOSE_FILE" run --rm robot-tests python run_tests.py --test "$TEST_FILE"
else
    print_status "Running all tests..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" run --rm robot-tests python run_tests.py
fi

# Check if results exist
if [[ -f "./results/output.xml" ]]; then
    print_status "Tests completed! Results available in ./results/"
    print_status "Open ./results/report.html in your browser to view the report"
else
    print_warning "No test results found. Check the logs above for errors."
fi

# Clean up
print_status "Cleaning up..."
docker-compose -f "$DOCKER_COMPOSE_FILE" down 2>/dev/null || true

print_status "Done!"
