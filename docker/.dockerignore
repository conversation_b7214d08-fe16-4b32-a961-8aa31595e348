# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Test results (will be generated in container)
results/
*.log
*.html
*.xml

# Temporary files
*.tmp
*.temp

# Documentation
*.md
README.md

# Jenkins
Jenkinsfile*

# Deploy directory itself (avoid recursion)
deploy/

# Cache
.cache/
.pytest_cache/