# Fast Robot Framework Docker - Minimal
FROM python:3.10-slim

# Environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    DISPLAY=:99 \
    DOCKER_ENV=true

# Install minimal dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    wget gnupg unzip curl xvfb \
    fonts-liberation libasound2 libatk-bridge2.0-0 libatk1.0-0 \
    libcups2 libdbus-1-3 libdrm2 libgtk-3-0 libnss3 \
    libxcomposite1 libxrandr2 libxss1 \
    && rm -rf /var/lib/apt/lists/*

# Install Chrome and ChromeDriver (fast)
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update && apt-get install -y --no-install-recommends google-chrome-stable \
    && CHROMEDRIVER_VERSION=$(curl -s https://googlechromelabs.github.io/chrome-for-testing/last-known-good-versions-with-downloads.json | grep -o '"version":"[^"]*' | head -1 | cut -d'"' -f4) \
    && wget -O /tmp/chromedriver.zip "https://storage.googleapis.com/chrome-for-testing-public/${CHROMEDRIVER_VERSION}/linux64/chromedriver-linux64.zip" \
    && unzip /tmp/chromedriver.zip -d /tmp/ \
    && mv /tmp/chromedriver-linux64/chromedriver /usr/local/bin/chromedriver \
    && chmod +x /usr/local/bin/chromedriver \
    && rm -rf /tmp/* /var/lib/apt/lists/*

WORKDIR /app

# Copy and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY run_tests.py .
COPY test_runner/ ./test_runner/
COPY test/ ./test/
COPY resources/ ./resources/
COPY pages/ ./pages/
COPY libs/ ./libs/

# Simple startup script
RUN echo '#!/bin/bash\nXvfb :99 -screen 0 1920x1080x24 &\nsleep 2\nexec "$@"' > /app/start.sh && chmod +x /app/start.sh

ENTRYPOINT ["/app/start.sh"]
CMD ["python", "run_tests.py"]