# Makefile for Robot Framework Docker operations

.PHONY: help build run run-dev test clean logs shell

# Default target
help:
	@echo "Available commands:"
	@echo "  build      - Build the Docker image"
	@echo "  run        - Run all tests in production mode"
	@echo "  run-dev    - Run tests in development mode"
	@echo "  test       - Run specific test (usage: make test TEST=test_file.robot)"
	@echo "  clean      - Clean up containers and images"
	@echo "  logs       - Show container logs"
	@echo "  shell      - Open shell in container"
	@echo "  viewer     - Start results viewer on http://localhost:8080"

# Build Docker image
build:
	@echo "Building Robot Framework Docker image..."
	docker-compose build --no-cache

# Run all tests in production mode
run:
	@echo "Running all tests..."
	docker-compose run --rm robot-tests

# Run tests in development mode
run-dev:
	@echo "Starting development environment..."
	docker-compose -f docker-compose.dev.yml up -d
	@echo "Container is running. Use 'make shell' to access it."

# Run specific test
test:
	@if [ -z "$(TEST)" ]; then \
		echo "Usage: make test TEST=test_file.robot"; \
		exit 1; \
	fi
	@echo "Running test: $(TEST)"
	docker-compose run --rm robot-tests robot --outputdir /app/results /app/test/$(TEST)

# Clean up containers and images
clean:
	@echo "Cleaning up containers..."
	docker-compose down --remove-orphans
	docker-compose -f docker-compose.dev.yml down --remove-orphans 2>/dev/null || true
	@echo "Removing unused images..."
	docker image prune -f

# Show container logs
logs:
	docker-compose logs -f robot-tests

# Open shell in container
shell:
	docker-compose -f docker-compose.dev.yml exec robot-tests-dev /bin/bash

# Start results viewer
viewer:
	@echo "Starting results viewer on http://localhost:8080"
	docker-compose --profile viewer up -d results-viewer
	@echo "Results viewer is available at: http://localhost:8080"

# Quick test run with build
quick:
	@echo "Quick test run (with fresh build)..."
	docker-compose build
	docker-compose run --rm robot-tests

# Stop all services
stop:
	docker-compose down
	docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
	docker-compose --profile viewer down 2>/dev/null || true
