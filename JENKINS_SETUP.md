# Jenkins Setup for Robot Framework Tests

## Jenkinsfiles Available

1. **`Jenkinsfile`** - Full pipeline with Robot Framework plugin
2. **`Jenkinsfile.simple`** - Basic pipeline (no plugins needed)
3. **`Jenkinsfile.multibranch`** - Multi-branch pipeline

## Quick Setup

### 1. Jenkins Requirements
- Docker installed on Jenkins agent
- Docker Compose installed
- HTML Publisher plugin (for reports)
- Robot Framework plugin (optional, for `Jenkinsfile`)

### 2. Create Pipeline Job
```
New Item → Pipeline → Configure
```

### 3. Pipeline Configuration
**Pipeline Definition:** Pipeline script from SCM
**SCM:** Git
**Repository URL:** Your repo URL
**Script Path:** `Jenkinsfile.simple` (recommended)

### 4. Build Triggers (Optional)
- ✅ GitHub hook trigger for GITScm polling
- ✅ Poll SCM: `H/5 * * * *` (every 5 minutes)

## Pipeline Features

### ✅ What it does:
- Builds Docker image
- Runs all Robot Framework tests
- Archives test results
- Publishes HTML reports
- Cleans up containers

### 📊 Outputs:
- **Test Report:** HTML report with results
- **Artifacts:** All test files (XML, HTML, screenshots)
- **Console:** Build logs and test output

### ⚡ Fast Execution:
- Uses `quick-test.sh` for speed
- Docker BuildKit enabled
- Parallel builds (multibranch)
- Automatic cleanup

## Usage Examples

### Manual Trigger
```bash
# In Jenkins UI
Build Now → View Console Output
```

### Webhook Trigger
```bash
# GitHub webhook URL
http://your-jenkins-url/github-webhook/
```

### API Trigger
```bash
curl -X POST http://your-jenkins-url/job/your-job/build \
  --user username:token
```

## Customization

### Add Email Notifications
Uncomment email section in Jenkinsfile:
```groovy
emailext (
    subject: "Test Failed: ${env.JOB_NAME}",
    body: "Check: ${env.BUILD_URL}",
    to: "<EMAIL>"
)
```

### Add Slack Notifications
```groovy
slackSend channel: '#testing',
          color: 'good',
          message: "Tests passed: ${env.JOB_NAME} #${env.BUILD_NUMBER}"
```

### Run Specific Tests
Modify `quick-test.sh` call:
```groovy
sh './quick-test.sh python run_tests.py --test specific_test.robot'
```

## Troubleshooting

### Docker Permission Issues
```bash
sudo usermod -aG docker jenkins
sudo systemctl restart jenkins
```

### Build Fails
- Check Docker is running
- Verify `docker-compose` is installed
- Check workspace permissions

### No Test Results
- Verify `docker/results/` directory exists
- Check test execution logs
- Ensure tests are actually running

## Performance Tips

- Use Jenkins agents with SSD storage
- Enable Docker BuildKit
- Use `quick-test.sh` for faster execution
- Clean up old builds regularly
