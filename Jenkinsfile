pipeline {
    agent any
    
    environment {
        DOCKER_BUILDKIT = '1'
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Build & Test') {
            steps {
                dir('docker') {
                    script {
                        // Build and run tests
                        sh 'docker-compose build'
                        sh 'bash quick-test.sh || true'  // Continue even if tests fail

                        // Check fail rate and set build status
                        def checkResult = sh(script: '''
                            if [ -f "results/output.xml" ]; then
                                # Parse XML to get test counts
                                TOTAL=$(grep -o "tests=\"[0-9]*\"" results/output.xml | head -1 | sed "s/tests=\\"\\([0-9]*\\)\\"/\\1/")
                                FAILED=$(grep -o "failures=\"[0-9]*\"" results/output.xml | head -1 | sed "s/failures=\\"\\([0-9]*\\)\\"/\\1/")

                                if [ "$TOTAL" -gt 0 ]; then
                                    FAIL_RATE=$((FAILED * 100 / TOTAL))
                                    echo "📊 Tests: $TOTAL, Failed: $FAILED, Fail Rate: $FAIL_RATE%"

                                    if [ $FAIL_RATE -gt 50 ]; then
                                        echo "❌ FAILED: Fail rate ($FAIL_RATE%) > 50%"
                                        exit 1
                                    else
                                        echo "✅ SUCCESS: Fail rate ($FAIL_RATE%) ≤ 50%"
                                        exit 0
                                    fi
                                else
                                    echo "❌ No tests found"
                                    exit 1
                                fi
                            else
                                echo "❌ No results file found"
                                exit 1
                            fi
                        ''', returnStatus: true)

                        if (checkResult != 0) {
                            currentBuild.result = 'FAILURE'
                            error('Build failed due to high test failure rate (>50%)')
                        }
                    }
                }
            }
        }
    }

    post {
                always {
                    // Archive test results
                    archiveArtifacts artifacts: 'docker/results/**/*', allowEmptyArchive: true
                    
                    // Publish Robot Framework results
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'docker/results',
                        reportFiles: 'report.html',
                        reportName: 'Robot Framework Report'
                    ])
                    

                }
            }
        }
    }
    
    post {
        always {
            // Clean up Docker containers
            dir('docker') {
                sh 'docker-compose down --remove-orphans || true'
            }
        }
        
        success {
            echo 'Tests passed successfully!'
        }
        
        failure {
            echo 'Tests failed!'
            // Send notification if needed
            // emailext (
            //     subject: "Test Failed: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
            //     body: "Test execution failed. Check console output at ${env.BUILD_URL}",
            //     to: "<EMAIL>"
            // )
        }
    }
}
