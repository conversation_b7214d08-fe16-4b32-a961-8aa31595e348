pipeline {
    agent any
    
    environment {
        DOCKER_BUILDKIT = '1'
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Build Docker Image') {
            steps {
                dir('docker') {
                    script {
                        sh 'docker-compose build'
                    }
                }
            }
        }
        
        stage('Run Tests') {
            steps {
                dir('docker') {
                    script {
                        sh './quick-test.sh'
                    }
                }
            }
            post {
                always {
                    // Archive test results
                    archiveArtifacts artifacts: 'docker/results/**/*', allowEmptyArchive: true
                    
                    // Publish Robot Framework results
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'docker/results',
                        reportFiles: 'report.html',
                        reportName: 'Robot Framework Report'
                    ])
                    
                    // Publish test results
                    step([
                        $class: 'RobotPublisher',
                        outputPath: 'docker/results',
                        outputFileName: 'output.xml',
                        reportFileName: 'report.html',
                        logFileName: 'log.html',
                        disableArchiveOutput: false,
                        passThreshold: 100,
                        unstableThreshold: 0,
                        onlyCritical: true,
                        otherFiles: ''
                    ])
                }
            }
        }
    }
    
    post {
        always {
            // Clean up Docker containers
            dir('docker') {
                sh 'docker-compose down --remove-orphans || true'
            }
        }
        
        success {
            echo 'Tests passed successfully!'
        }
        
        failure {
            echo 'Tests failed!'
            // Send notification if needed
            // emailext (
            //     subject: "Test Failed: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
            //     body: "Test execution failed. Check console output at ${env.BUILD_URL}",
            //     to: "<EMAIL>"
            // )
        }
    }
}
