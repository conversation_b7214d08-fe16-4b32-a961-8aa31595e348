*** Settings ***
Library    SeleniumLibrary
Library    String
Library    Collections
# Library    RPA.Netsuite
# Library    RPA.Desktop
Library    BuiltIn
# Library    ExcelLibrary
Resource    ../pages/page_object/login_page.robot
Resource    ../pages/page_object/create_course_page.robot
#Resource    ../test/login_tests.robot
Resource    ../resources/browser_setup.robot
Resource    ../resources/variables.robot
Variables    ../libs/data_reader.py
Resource    ../pages/page_object/dashboard_page.robot
#Test Setup    Open Browser To Login Page
Suite Setup    Create Suite Setup In Course Test
Suite Teardown    Close Browser and Quit

*** Test Cases ***
Tcs 01: Func-Course-03: Create Course with fully field
    [Documentation]    Kiểm tra việc nhập giá trị vào các trường của khoá học
    [Tags]    smoke    create course

    Go to create course
    Enter Course Detail Form    Block chain    BL01    Block chain    3    Khoá học cơ bản    Elective    Active    BL2 - block1
    Click Create Course Button
Tcs 02: Func-Course-04: Create course with empty required fields
    [Documentation]    Tạo một khoá học mới bằng cách bỏ trống các trường bắt buộc
    [Tags]    smoke    create course   
    #Login User    ${USERNAME_ADMIN}    ${PASSWORD_ADMIN}
    Go To Create Course 
    Empty Required Fields With Course Form

Tcs 11: Func-Course: Create Course with maximum credits value
    [Documentation]    Kiểm tra việc nhập giá trị lớn nhất vào trường tín chỉ của khoá học
    [Tags]    smoke    create course
    #Login User    ${USERNAME_ADMIN}    ${PASSWORD_ADMIN}

    Go to create course
    Enter Course Detail Form    Block chain    BL01    Block chain    3    Khoá học cơ bản    Elective    Active    BL2 - block1
    Click Create Course Button

Tcs 03: Func-CLO-03: Create CLOs with fully fields
    [Documentation]    Kiểm tra việc nhập giá trị vào các trường của mục tiêu khoá học
    [Tags]    smoke    create clo
    #Login User    ${USERNAME_ADMIN}    ${PASSWORD_ADMIN}
    Go To Create CLO
    Enter Course Learning Outcome Form    as1 - abc    CLO01    Mục tiêu khoá học 01    Skill    Remember    10    Quiz 
    Click Create CLOs button

Tcs 04: Func-CLO-04: Create CLOS with empty required fields
    [Documentation]    Tạo một mục tiêu khoá học mới bằng cách bỏ trống các trường bắt buộc
    [Tags]    smoke    create clo

    Go To Create CLO 
    Empty Required Fields With CLOs

Tcs 15: Func-CLO-05: Create CLOs with negative "weight" field value
    [Documentation]    Kiểm tra việc nhập giá trị vào các trường của mục tiêu khoá học
    [Tags]    smoke    create clo
    #Login User    ${USERNAME_ADMIN}    ${PASSWORD_ADMIN}
    Go To Create CLO
    Enter Course Learning Outcome Form    1234 - Abc    CLO01    Mục tiêu khoá học 01
    ...    Skill    Remember    -1    Quiz 
    Click Create CLOs button
    # Element Should Contain    locator    expected    message=This field must be greater than or equal to 0.
    # Tcs 05: Get data course table
#     [Documentation]    Lấy dữ liệu từ bảng khoá học
#     [Tags]    smoke    get course table
#     #Login User    ${USERNAME_ADMIN}    ${PASSWORD_ADMIN}
#     View Course Name From Table
#     Save Course Table Data To Excel

Tcs 06: Edit Course
    [Documentation]    Chỉnh sửa thông tin khoá học đã tạo
    [Tags]    smoke    edit course
    #Login User    ${USERNAME_ADMIN}    ${PASSWORD_ADMIN}
    View Course Name From Table
    Edit Course
    Log To Console    message=Đã chỉnh sửa khoá học

Tcs 08: Compare value CLO in course
    [Documentation]    So sánh giá trị CLO đã định nghĩa và danh sách trong clo trong khoá học
    [Tags]    smoke    compare clo
    #Login User    ${USERNAME_ADMIN}    ${PASSWORD_ADMIN}
    View Course Name From Table
    Click Element    ${VIEWCOURSE_BTN}
    Compare value CLO

# Tcs 07: Delete Course
#     [Documentation]    Xoá khoá học đã tạo
#     #[Teardown]    Capture Page Screenshot
#     [Tags]    smoke    delete course
#     #Login User    ${USERNAME_ADMIN}    ${PASSWORD_ADMIN}
#     View Course Name From Table
#     Delete Course
#     Log To Console    message=Đã xoá khoá học
TC 09: Edit All Fields In Course
    [Documentation]    Kiểm tra chỉnh sửa giá trị vào các trường của khoá học
    [Tags]    smoke    create course
    #Login User    ${USERNAME_ADMIN}    ${PASSWORD_ADMIN}
    View Course Name From Table
    Click Element   ${EDITCOURSE_BTN}
    Edit Course Detail Form    Block chain    BL01    Block chain    5    Khoá học cơ bản    Elective    Active    BL2 - block1
    Click update button in course page_object    

# Tc 10: Get data CLO table
#     [Documentation]    Lấy dữ liệu từ bảng CLO
#     [Tags]    smoke    get clo table
#     #Login User    ${USERNAME_ADMIN}    ${PASSWORD_ADMIN}
#     View Course Name From Table
#     Click Element    ${CLOs_BTN}
#     Save CLO Table Data To Excel

Tcs 12: "Tạo mới" button inactive
    [Documentation]    Kiểm tra nếu đã có dữ liệu thì nút "Tạo mới" không hiển thị và không thể click
    [Tags]    smoke    create course
    Go to create course
    View Course Name From Table
    Click Element   ${VIEWCOURSE_BTN}

    ${course_count}=    Get Element Count    ${SCHEDULE_COURSE_TABLE}
    Should Be True    ${course_count} > 0    Đã có lịch trình trong bảng
    Run Keyword And Expect Error    *    Wait Until Element Is Visible    ${CREATENEW_BTN}    timeout=3s


Tc 13: Find word with valid and invalid Keyword
    [Documentation]    Tìm kiếm từ khoá hợp lệ trong danh sách khoá học
    [Tags]    smoke    find word
    #Login User    ${USERNAME_ADMIN}    ${PASSWORD_ADMIN}
    Wait Until Element Is Visible    ${HOCPHAN_MENUBAR}    timeout=10s
    Click Element    ${HOCPHAN_MENUBAR}
    FOR    ${element}    IN    @{LIST_FIND_COURSE_KEYWORD}
        Wait Until Element Is Visible    ${FIELD_SEARCH_COURSE}    timeout=10s
        Input Text    ${FIELD_SEARCH_COURSE}    ${element}
        Click Element    ${COURSE_FIND_ICON}
        Wait Until Page Contains    ${element}
        ${found}=    Run Keyword And Return Status    Element Should Be Visible    ${COURSE_NOT_FOUND}
        IF    ${found} 
            Log    Không tìm thấy từ khoá: ${element}
        ELSE
            Log    Từ khoá tìm kiếm: ${element}
        END
    END
    
    
*** Keywords ***
Create Suite Setup In Course Test
    # [Arguments]    ${username}    ${password}
    Open Browser To Login Page
    Login User    ${USERNAME_ADMIN}    ${PASSWORD_ADMIN}
    # Go To Categories    Học phần
